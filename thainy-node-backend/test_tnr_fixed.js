// Test script to verify the fixed TNR generation
// Run this after installing Node.js: node test_tnr_fixed.js

const path = require('path');

// Mock database for testing
const mockDb = {
  sequelize: {
    query: async (query, options) => {
      console.log('Mock DB Query:', query);
      
      // Simulate existing TNRs for testing
      if (query.includes('202507')) {
        return [{ TNR: '20250700004' }]; // Simulate last TNR is 20250700004
      } else if (query.includes('202501')) {
        return [{ TNR: '20250100123' }]; // Simulate last TNR for January
      }
      
      return []; // No existing TNRs
    }
  },
  Sequelize: {
    QueryTypes: {
      SELECT: 'SELECT'
    }
  }
};

// Mock the generateTNR function with our logic
async function generateTNR(registDate, hospital) {
  try {
    // Parse the registration date
    const date = new Date(registDate);
    if (isNaN(date.getTime())) {
      throw new Error('Invalid registration date format');
    }

    // Format date components for TNR prefix (YYYYMM)
    const year = date.getFullYear().toString();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const datePrefix = `${year}${month}`;
    
    // Find the highest existing TNR for this year-month combination
    const existingTNRs = await mockDb.sequelize.query(
      `SELECT TNR FROM (
        SELECT TNR FROM patient_concurrence WHERE TNR LIKE '${datePrefix}%'
        UNION ALL
        SELECT TNR FROM patient WHERE TNR LIKE '${datePrefix}%'
        UNION ALL
        SELECT TNR FROM register_criteria WHERE TNR LIKE '${datePrefix}%'
      ) AS all_tnrs ORDER BY TNR DESC LIMIT 1`,
      {
        type: mockDb.Sequelize.QueryTypes.SELECT,
      }
    );
    
    let nextSequence = 1;
    
    if (existingTNRs.length > 0) {
      const lastTNR = existingTNRs[0].TNR;
      console.log('Last existing TNR:', lastTNR);
      // Extract the last 5 digits and increment
      const lastSequence = parseInt(lastTNR.slice(-5), 10);
      nextSequence = lastSequence + 1;
    }
    
    // Format sequence number to 5 digits with leading zeros
    const sequenceStr = nextSequence.toString().padStart(5, '0');
    
    // Generate TNR: YYYYMMXXXXX (11 digits total)
    const tnr = `${datePrefix}${sequenceStr}`;
    
    return tnr;
  } catch (error) {
    console.error('Error generating TNR:', error);
    // Fallback TNR generation if database query fails
    const timestamp = Date.now().toString();
    const fallbackTNR = timestamp.slice(-11); // Last 11 digits of timestamp
    return fallbackTNR;
  }
}

// Test cases
async function runTests() {
  console.log('Testing Fixed TNR Generation...\n');

  const testCases = [
    { registDate: '2025-07-15', hospital: 'HOSPITAL_A', expected: '20250700005' },
    { registDate: '2025-01-15', hospital: 'HOSPITAL_B', expected: '20250100124' },
    { registDate: '2025-12-31', hospital: 'MAIN_HOSPITAL', expected: '20251200001' },
    { registDate: '2025-07-01', hospital: 'TEST_HOSPITAL', expected: '20250700005' },
  ];

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    console.log(`Test ${i + 1}:`);
    console.log(`Input: registDate="${testCase.registDate}", hospital="${testCase.hospital}"`);
    console.log(`Expected: ${testCase.expected}`);
    
    try {
      const tnr = await generateTNR(testCase.registDate, testCase.hospital);
      console.log(`Generated TNR: ${tnr}`);
      console.log(`TNR Length: ${tnr.length}`);
      console.log(`Format Check: ${tnr.length === 11 ? 'PASS' : 'FAIL'} (should be 11 digits)`);
      
      if (testCase.expected && tnr === testCase.expected) {
        console.log('Result: PASS ✓');
      } else if (testCase.expected) {
        console.log('Result: FAIL ✗ (sequence might differ due to mock data)');
      } else {
        console.log('Result: Generated successfully');
      }
    } catch (error) {
      console.log(`Error: ${error.message}`);
    }
    
    console.log('---\n');
  }

  console.log('TNR Generation Test Complete!');
  console.log('\nExpected TNR format: YYYYMMXXXXX (11 digits)');
  console.log('- YYYY: Year (4 digits)');
  console.log('- MM: Month (2 digits)');
  console.log('- XXXXX: Sequential number (5 digits with leading zeros)');
}

// Run the tests
runTests().catch(console.error);
