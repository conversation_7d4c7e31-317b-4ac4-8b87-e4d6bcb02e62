# API Usage Examples

## Register Criteria API

### Create Register Criteria (Supports Both Formats)

**Endpoint**: `POST /register-criteria/create`

#### Option 1: Direct JSON Object (Recommended)

**Request Body**:
```json
{
  "TNR": "20250300001",
  "ward": "ICU",
  "pna": "PNA001",
  "patient_symptoms": "Respiratory distress, fever"
}
```

**cURL Example**:
```bash
curl -X POST http://localhost:3000/register-criteria/create \
  -H "Content-Type: application/json" \
  -d '{
    "TNR": "20250300001",
    "ward": "ICU",
    "pna": "PNA001",
    "patient_symptoms": "Respiratory distress, fever"
  }'
```

#### Option 2: Legacy PHP Format (JSON String)

**Request Body**:
```json
{
  "isdata": "{\"TNR\":\"20250300001\",\"ward\":\"ICU\",\"pna\":\"PNA001\",\"patient_symptoms\":\"Respiratory distress, fever\"}"
}
```

**cURL Example**:
```bash
curl -X POST http://localhost:3000/register-criteria/create \
  -H "Content-Type: application/json" \
  -d '{
    "isdata": "{\"TNR\":\"20250300001\",\"ward\":\"ICU\",\"pna\":\"PNA001\",\"patient_symptoms\":\"Respiratory distress, fever\"}"
  }'
```

#### Response (Same for Both Formats)

```json
{
  "TNR": "20250300001",
  "ward": "ICU",
  "pna": "PNA001",
  "patient_symptoms": "Respiratory distress, fever"
}
```

**PHP Equivalent**:
```php
<?php
   $data = $_POST["isdata"];
   $mydata = json_decode($data);
   add_to_database("register_criteria", $mydata);
   echo $mydata;
?>
```

---

## Patient Creation API

### Create Patient (JSON Object Format)

**Endpoint**: `POST /patients/create`

**Headers**:
```
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

**Request Body** (JSON Object):

**Minimal Example** (required fields only - others will use defaults):
```json
{
  "TNR": "20250300001",
  "fullname": "John Doe",
  "HN": "HN001"
}
```

**Recommended Example** (with important fields):
```json
{
  "TNR": "20250300001",
  "fullname": "John Doe",
  "HN": "HN001",
  "DOB": "2025-01-15",
  "sex": "Male",
  "mother_fullname": "Jane Doe",
  "status": "active"
}
```

**Complete Example** (all fields):
```json
{
  "TNR": "20250300001",
  "fullname": "John Doe",
  "HN": "HN001",
  "infant_id": "INF001",
  "passport_id": "P123456",
  "illegal": "No",
  "illegal_id": "",
  "illegal_hospital": "",
  "DOB": "2025-01-15",
  "sex": "Male",
  "ethnic": "Asian",
  "mother_fullname": "Jane Doe",
  "mother_id": "M123456",
  "mother_passport": "MP123456",
  "mother_address": "123 Main St",
  "mother_tel": "************",
  "contact_person_name": "Emergency Contact",
  "relation": "Father",
  "contact_person_tel": "************",
  "other_contact": "Other info",
  "status": "active"
}
```

**Response**:
```json
"ok"
```

**cURL Example**:
```bash
curl -X POST http://localhost:3000/patients/create \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "TNR": "20250300001",
    "fullname": "John Doe",
    "HN": "HN001",
    "DOB": "2025-01-15",
    "sex": "Male",
    "mother_fullname": "Jane Doe",
    "status": "active"
  }'
```

**What the API does**:
1. Adds `created_date` timestamp to patient data
2. Inserts patient record into `patient` table
3. Updates `hospital` field for the TNR
4. Creates `section_progress` record with TNR, hospital, and timestamp
5. Returns "ok" on success

**PHP Equivalent**:
```php
<?php
   $TNR = $_POST["TNR"];
   $data = $_POST["mydata"];
   $mydata = json_decode($data);
   $created_date = date("Y-m-d H:i:s");
   array_push($mydata, $created_date);

   add_to_database("patient", $mydata);
   $hospital = $_SESSION["hospital"];

   mysql_query("UPDATE patient SET hospital = '$hospital' WHERE TNR = '$TNR' ");
   mysql_query("INSERT INTO section_progress (TNR,hospital,time) values ('$TNR','$hospital','$created_date') ");
   echo "ok";
?>
```

---

## TNR Generation API

### Generate TNR

**Endpoint**: `POST /patients/generate-tnr`

**Headers**: 
```
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

**Request Body**:
```json
{
  "regist_date": "2025-03-15"
}
```

**Response** (Success):
```json
"20250300001"
```

**Response** (Failure):
```json
0
```

---

## Generic Database Utility

The `addToDatabase` function can be used for any table:

```javascript
const { addToDatabase } = require('../utils/databaseUtils');

// Example usage
const data = {
  TNR: "20250300001",
  ward: "ICU",
  pna: "PNA001", 
  patient_symptoms: "Respiratory distress"
};

const result = await addToDatabase('register_criteria', data);
```

### Supported Tables

The generic utility supports all major tables:
- `register_criteria`
- `patient`
- `patient_concurrence`
- `user_concurrence`
- `register_progress`
- `section1`, `section2`, `section3`, `section4`
- `section5_15_day`, `section5_1_year`, `section5_30_day`
- `section5_3_main`, `section5_4`, `section5_6_main`
- `section5_normal_newborn`
- `refer`
- `hospital`
- `user`
- `admin`
- And more...

---

## Error Responses

All APIs return consistent error responses:

```json
{
  "status": 0,
  "message": "Error description"
}
```

Common HTTP status codes:
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (missing/invalid token)
- `404`: Not Found (resource doesn't exist)
- `409`: Conflict (duplicate key)
- `500`: Internal Server Error

---

## Testing the APIs

1. **Start the server**:
   ```bash
   npm run dev
   ```

2. **Test with cURL or Postman**

3. **For authenticated endpoints**, first login to get a JWT token:
   ```bash
   curl -X POST http://localhost:3000/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username":"your_username","password":"your_password"}'
   ```

4. **Use the token in subsequent requests**:
   ```bash
   curl -X POST http://localhost:3000/patients/generate-tnr \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"regist_date":"2025-03-15"}'
   ```
