# API Usage Examples

## Register Criteria API

### 1. Create Register Criteria (Matches PHP functionality)

**Endpoint**: `POST /register-criteria/create`

**Request Body**:
```json
{
  "isdata": "{\"TNR\":\"20250300001\",\"ward\":\"ICU\",\"pna\":\"PNA001\",\"patient_symptoms\":\"Respiratory distress, fever\"}"
}
```

**Response** (Success):
```json
{
  "TNR": "20250300001",
  "ward": "ICU", 
  "pna": "PNA001",
  "patient_symptoms": "Respiratory distress, fever"
}
```

**cURL Example**:
```bash
curl -X POST http://localhost:3000/register-criteria/create \
  -H "Content-Type: application/json" \
  -d '{
    "isdata": "{\"TNR\":\"20250300001\",\"ward\":\"ICU\",\"pna\":\"PNA001\",\"patient_symptoms\":\"Respiratory distress, fever\"}"
  }'
```

### 2. Get Register Criteria by TNR

**Endpoint**: `GET /register-criteria/:tnr`

**Example**: `GET /register-criteria/20250300001`

**Response**:
```json
{
  "TNR": "20250300001",
  "ward": "ICU",
  "pna": "PNA001", 
  "patient_symptoms": "Respiratory distress, fever"
}
```

### 3. Get All Register Criteria (with pagination and filtering)

**Endpoint**: `GET /register-criteria`

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10, max: 100)
- `ward`: Filter by ward
- `pna`: Filter by PNA
- `patient_symptoms`: Filter by symptoms (partial match)

**Example**: `GET /register-criteria?page=1&limit=10&ward=ICU`

**Response**:
```json
{
  "totalItems": 25,
  "results": [
    {
      "TNR": "20250300001",
      "ward": "ICU",
      "pna": "PNA001",
      "patient_symptoms": "Respiratory distress, fever"
    }
  ],
  "totalPages": 3,
  "currentPage": 1
}
```

### 4. Update Register Criteria (Requires Authentication)

**Endpoint**: `PUT /register-criteria/:tnr`

**Headers**: 
```
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

**Request Body**:
```json
{
  "ward": "NICU",
  "patient_symptoms": "Updated symptoms"
}
```

### 5. Delete Register Criteria (Requires Authentication)

**Endpoint**: `DELETE /register-criteria/:tnr`

**Headers**: 
```
Authorization: Bearer <your-jwt-token>
```

---

## TNR Generation API

### Generate TNR

**Endpoint**: `POST /patients/generate-tnr`

**Headers**: 
```
Authorization: Bearer <your-jwt-token>
Content-Type: application/json
```

**Request Body**:
```json
{
  "regist_date": "2025-03-15"
}
```

**Response** (Success):
```json
"20250300001"
```

**Response** (Failure):
```json
0
```

---

## Generic Database Utility

The `addToDatabase` function can be used for any table:

```javascript
const { addToDatabase } = require('../utils/databaseUtils');

// Example usage
const data = {
  TNR: "20250300001",
  ward: "ICU",
  pna: "PNA001", 
  patient_symptoms: "Respiratory distress"
};

const result = await addToDatabase('register_criteria', data);
```

### Supported Tables

The generic utility supports all major tables:
- `register_criteria`
- `patient`
- `patient_concurrence`
- `user_concurrence`
- `register_progress`
- `section1`, `section2`, `section3`, `section4`
- `section5_15_day`, `section5_1_year`, `section5_30_day`
- `section5_3_main`, `section5_4`, `section5_6_main`
- `section5_normal_newborn`
- `refer`
- `hospital`
- `user`
- `admin`
- And more...

---

## Error Responses

All APIs return consistent error responses:

```json
{
  "status": 0,
  "message": "Error description"
}
```

Common HTTP status codes:
- `400`: Bad Request (validation errors)
- `401`: Unauthorized (missing/invalid token)
- `404`: Not Found (resource doesn't exist)
- `409`: Conflict (duplicate key)
- `500`: Internal Server Error

---

## Testing the APIs

1. **Start the server**:
   ```bash
   npm run dev
   ```

2. **Test with cURL or Postman**

3. **For authenticated endpoints**, first login to get a JWT token:
   ```bash
   curl -X POST http://localhost:3000/auth/login \
     -H "Content-Type: application/json" \
     -d '{"username":"your_username","password":"your_password"}'
   ```

4. **Use the token in subsequent requests**:
   ```bash
   curl -X POST http://localhost:3000/patients/generate-tnr \
     -H "Authorization: Bearer YOUR_JWT_TOKEN" \
     -H "Content-Type: application/json" \
     -d '{"regist_date":"2025-03-15"}'
   ```
