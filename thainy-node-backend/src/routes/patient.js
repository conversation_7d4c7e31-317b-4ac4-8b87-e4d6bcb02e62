const express = require('express');
const router = express.Router();
const { getAllPatients, filterPatients, getPatientByTnrAndHospital, getSectionProgressByTnrAndHospital, getRegisterProgressByTnr<PERSON><PERSON><PERSON>, countReferByTnrAndHospitalHandler, cancelReferByTnrAndHospitalHandler, updateReferStatusToReject2Handler, setPatientStatusInactiveHandler, generateTNRHandler } = require('../controllers/patientController');
const authenticateToken = require('../middlewares/auth');

router.get('/patients-list', authenticateToken, getAllPatients);
router.post('/search', authenticateToken, filterPatients);
router.post('/by-tnr-hospital', getPatientByTnrAndHospital);
router.post('/section-progress', getSectionProgressByTnrAndHospital);
router.post('/register-progress', getRegisterProgressByTnrHandler);
router.post('/refer-count', countReferByTnrAndHospitalHandler);
router.post('/cancel-refer', cancelReferByTnrAndHospitalHandler);
router.post('/reject2-refer', updateReferStatusToReject2Handler);
router.post('/set-inactive', authenticateToken, setPatientStatusInactiveHandler);
router.post('/generate-tnr', authenticateToken, generateTNRHandler);

module.exports = router; 