const express = require('express');
const router = express.Router();
const { 
  createRegisterCriteria<PERSON><PERSON><PERSON>, 
  getRegisterCriteriaByTNRHandler, 
  updateRegisterCriteriaHand<PERSON>, 
  deleteRegisterCriteriaHandler, 
  getAllRegisterCriteriaHandler 
} = require('../controllers/registerCriteriaController');
const authenticateToken = require('../middlewares/auth');

// Create register criteria (matches the PHP functionality)
router.post('/create', createRegisterCriteriaHandler);

// Get register criteria by TNR
router.get('/:tnr', getRegisterCriteriaByTNRHandler);

// Get all register criteria with optional filtering
router.get('/', getAllRegisterCriteriaHandler);

// Update register criteria by TNR (authenticated)
router.put('/:tnr', authenticateToken, updateRegisterCriteriaHandler);

// Delete register criteria by TNR (authenticated)
router.delete('/:tnr', authenticateToken, deleteRegisterCriteriaHandler);

module.exports = router;
