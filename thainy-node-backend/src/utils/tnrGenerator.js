const db = require('../models');

/**
 * Generates a TNR (Treatment Number Record) based on registration date
 * Creates an 11-digit TNR in format YYYYMMXXXXX where XXXXX is sequential
 * @param {string} registDate - Registration date in format YYYY-MM-DD
 * @param {string} hospital - Hospital identifier (not used in TNR generation but kept for compatibility)
 * @returns {Promise<string>} Generated TNR
 */
async function generateTNR(registDate, hospital) {
  try {
    // Parse the registration date
    const date = new Date(registDate);
    if (isNaN(date.getTime())) {
      throw new Error('Invalid registration date format');
    }

    // Format date components for TNR prefix (YYYYMM)
    const year = date.getFullYear().toString();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const datePrefix = `${year}${month}`;

    // Find the highest existing TNR for this year-month combination
    // Check both patient_concurrence and patient tables to ensure uniqueness
    const existingTNRs = await db.sequelize.query(
      `SELECT TNR FROM (
        SELECT TNR FROM patient_concurrence WHERE TNR LIKE '${datePrefix}%'
        UNION ALL
        SELECT TNR FROM patient WHERE TNR LIKE '${datePrefix}%'
        UNION ALL
        SELECT TNR FROM register_criteria WHERE TNR LIKE '${datePrefix}%'
      ) AS all_tnrs ORDER BY TNR DESC LIMIT 1`,
      {
        type: db.Sequelize.QueryTypes.SELECT,
      }
    );

    let nextSequence = 1;

    if (existingTNRs.length > 0) {
      const lastTNR = existingTNRs[0].TNR;
      // Extract the last 5 digits and increment
      const lastSequence = parseInt(lastTNR.slice(-5), 10);
      nextSequence = lastSequence + 1;
    }

    // Format sequence number to 5 digits with leading zeros
    const sequenceStr = nextSequence.toString().padStart(5, '0');

    // Generate TNR: YYYYMMXXXXX (11 digits total)
    const tnr = `${datePrefix}${sequenceStr}`;

    return tnr;
  } catch (error) {
    // Fallback TNR generation if database query fails
    const timestamp = Date.now().toString();
    const fallbackTNR = timestamp.slice(-11); // Last 11 digits of timestamp
    return fallbackTNR;
  }
}

module.exports = { generateTNR };
