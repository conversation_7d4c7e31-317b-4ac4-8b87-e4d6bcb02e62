const crypto = require('crypto');

/**
 * Generates a TNR (Treatment Number Record) based on registration date and hospital
 * This function mimics the PHP generateTNR function logic
 * @param {string} registDate - Registration date in format YYYY-MM-DD
 * @param {string} hospital - Hospital identifier
 * @returns {string} Generated TNR
 */
function generateTNR(registDate, hospital) {
  try {
    // Parse the registration date
    const date = new Date(registDate);
    if (isNaN(date.getTime())) {
      throw new Error('Invalid registration date format');
    }

    // Format date components
    const year = date.getFullYear().toString();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    
    // Create a base string with date and hospital
    const baseString = `${year}${month}${day}_${hospital}`;
    
    // Add timestamp and random component for uniqueness
    const timestamp = Date.now().toString();
    const randomComponent = crypto.randomBytes(4).toString('hex').toUpperCase();
    
    // Generate TNR with format: YYYYMMDD_HOSPITAL_TIMESTAMP_RANDOM
    const tnr = `${baseString}_${timestamp.slice(-6)}_${randomComponent}`;
    
    // Ensure TNR doesn't exceed database field length (100 chars based on model)
    return tnr.length > 100 ? tnr.substring(0, 100) : tnr;
  } catch (error) {
    // Fallback TNR generation if date parsing fails
    const timestamp = Date.now().toString();
    const randomComponent = crypto.randomBytes(6).toString('hex').toUpperCase();
    return `TNR_${hospital}_${timestamp}_${randomComponent}`.substring(0, 100);
  }
}

module.exports = { generateTNR };
