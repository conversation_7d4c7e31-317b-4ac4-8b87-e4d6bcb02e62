const db = require('../models');
const bcrypt = require('bcryptjs');
const logger = require('./logger');
const { generateTNR } = require('./tnrGenerator');

/**
 * Generic function to add data to any database table
 * Mimics the PHP add_to_database function logic
 * @param {string} tableName - Name of the table to insert into
 * @param {Array|Object} data - Data to insert (array for ordered values, object for key-value pairs)
 * @param {boolean} autoIncrement - Whether to skip the first field (for auto-increment IDs)
 * @returns {Promise<Object>} Created record
 */
async function addToDatabase(tableName, data, autoIncrement = false) {
  try {
    // Get the Sequelize model for the table
    const Model = getModelByTableName(tableName);
    if (!Model) {
      throw new Error(`Model not found for table: ${tableName}`);
    }

    // Convert array data to object if needed
    let insertData = {};
    
    if (Array.isArray(data)) {
      // Get model attributes to map array values to field names
      const attributes = Object.keys(Model.rawAttributes);
      
      // Skip first attribute if autoIncrement is true (for ID fields)
      const startIndex = autoIncrement ? 1 : 0;
      const fieldsToUse = attributes.slice(startIndex);
      
      // Map array values to field names
      for (let i = 0; i < data.length && i < fieldsToUse.length; i++) {
        const fieldName = fieldsToUse[i];
        let value = data[i];
        
        // Handle password hashing (like PHP md5 logic)
        if (fieldName === 'password' && value) {
          value = await bcrypt.hash(value, 10); // More secure than MD5
        }
        
        insertData[fieldName] = value;
      }
    } else if (typeof data === 'object') {
      // Data is already an object
      insertData = { ...data };
      
      // Handle password hashing
      if (insertData.password) {
        insertData.password = await bcrypt.hash(insertData.password, 10);
      }
    } else {
      throw new Error('Data must be an array or object');
    }

    // Create the record
    const result = await Model.create(insertData);
    
    logger.info(`Successfully inserted data into ${tableName}:`, result.toJSON());
    return result;
    
  } catch (error) {
    logger.error(`Error inserting data into ${tableName}:`, error);
    throw error;
  }
}

/**
 * Get Sequelize model by table name
 * @param {string} tableName - Database table name
 * @returns {Object|null} Sequelize model
 */
function getModelByTableName(tableName) {
  const modelMap = {
    'register_criteria': db.RegisterCriteria,
    'patient': db.Patient,
    'patient_concurrence': db.PatientConcurrence,
    'user_concurrence': db.UserConcurrence,
    'register_progress': db.RegisterProgress,
    'section1': db.Section1,
    'section2': db.Section2,
    'section3': db.Section3,
    'section4': db.Section4,
    'section5_15_day': db.Section515Day,
    'section5_1_year': db.Section51Year,
    'section5_30_day': db.Section530Day,
    'section5_3_main': db.Section53Main,
    'section5_4': db.Section54,
    'section5_6_main': db.Section56Main,
    'section5_normal_newborn': db.Section5NormalNewborn,
    'refer': db.Refer,
    'hospital': db.Hospital,
    'user': db.User,
    'admin': db.Admin,
    'pin': db.Pin,
    'chat_history': db.ChatHistory,
    'forget_password_token': db.ForgetPasswordToken,
    'register_token': db.RegisterToken,
    'noti_15d': db.Noti15D,
    'noti_30d': db.Noti30D,
    'noti_1y': db.Noti1Y,
  };
  
  return modelMap[tableName.toLowerCase()] || null;
}

/**
 * Bulk insert multiple records into a table
 * @param {string} tableName - Name of the table
 * @param {Array} dataArray - Array of data objects to insert
 * @param {boolean} autoIncrement - Whether to skip ID field
 * @returns {Promise<Array>} Array of created records
 */
async function bulkAddToDatabase(tableName, dataArray, autoIncrement = false) {
  try {
    const results = [];
    
    for (const data of dataArray) {
      const result = await addToDatabase(tableName, data, autoIncrement);
      results.push(result);
    }
    
    return results;
  } catch (error) {
    logger.error(`Error in bulk insert to ${tableName}:`, error);
    throw error;
  }
}

module.exports = {
  addToDatabase,
  bulkAddToDatabase,
  getModelByTableName,
  generateTNR
};
