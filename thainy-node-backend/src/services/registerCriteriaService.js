const db = require('../models');
const logger = require('../utils/logger');
const { addToDatabase } = require('../utils/databaseUtils');

/**
 * Creates a new register criteria record
 * @param {Object|Array} criteriaData - The criteria data to insert
 * @returns {Promise<Object>} Created register criteria record
 */
async function createRegisterCriteria(criteriaData) {
  try {
    // Use the generic addToDatabase function
    const result = await addToDatabase('register_criteria', criteriaData);
    
    logger.info('Successfully created register criteria:', result.toJSON());
    return result;
  } catch (error) {
    logger.error('Error creating register criteria:', error);
    throw error;
  }
}

/**
 * Gets register criteria by TNR
 * @param {string} TNR - The TNR to search for
 * @returns {Promise<Object|null>} Register criteria record or null
 */
async function getRegisterCriteriaByTNR(TNR) {
  try {
    const criteria = await db.RegisterCriteria.findOne({ where: { TNR } });
    return criteria;
  } catch (error) {
    logger.error(`Error fetching register criteria for TNR ${TNR}:`, error);
    throw error;
  }
}

/**
 * Updates register criteria by TNR
 * @param {string} TNR - The TNR to update
 * @param {Object} updateData - Data to update
 * @returns {Promise<Array>} Array with number of affected rows and updated records
 */
async function updateRegisterCriteria(TNR, updateData) {
  try {
    const result = await db.RegisterCriteria.update(updateData, {
      where: { TNR },
      returning: true
    });
    
    logger.info(`Updated register criteria for TNR ${TNR}:`, result);
    return result;
  } catch (error) {
    logger.error(`Error updating register criteria for TNR ${TNR}:`, error);
    throw error;
  }
}

/**
 * Deletes register criteria by TNR
 * @param {string} TNR - The TNR to delete
 * @returns {Promise<number>} Number of deleted records
 */
async function deleteRegisterCriteria(TNR) {
  try {
    const result = await db.RegisterCriteria.destroy({ where: { TNR } });
    
    logger.info(`Deleted register criteria for TNR ${TNR}, affected rows: ${result}`);
    return result;
  } catch (error) {
    logger.error(`Error deleting register criteria for TNR ${TNR}:`, error);
    throw error;
  }
}

/**
 * Gets all register criteria with optional filtering
 * @param {Object} filters - Optional filters
 * @param {number} limit - Limit for pagination
 * @param {number} offset - Offset for pagination
 * @returns {Promise<Object>} Object with count and rows
 */
async function getAllRegisterCriteria(filters = {}, limit = 50, offset = 0) {
  try {
    const whereClause = {};
    
    // Add filters if provided
    if (filters.ward) {
      whereClause.ward = filters.ward;
    }
    if (filters.pna) {
      whereClause.pna = filters.pna;
    }
    if (filters.patient_symptoms) {
      whereClause.patient_symptoms = { [db.Sequelize.Op.like]: `%${filters.patient_symptoms}%` };
    }
    
    const result = await db.RegisterCriteria.findAndCountAll({
      where: whereClause,
      limit,
      offset,
      order: [['TNR', 'DESC']]
    });
    
    return result;
  } catch (error) {
    logger.error('Error fetching all register criteria:', error);
    throw error;
  }
}

module.exports = {
  createRegisterCriteria,
  getRegisterCriteriaByTNR,
  updateRegisterCriteria,
  deleteRegisterCriteria,
  getAllRegisterCriteria
};
