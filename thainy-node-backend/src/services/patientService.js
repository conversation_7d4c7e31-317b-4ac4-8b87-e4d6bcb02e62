const db = require('../models');
const logger = require('../utils/logger');
const { Op, Sequelize } = require('sequelize');
const { generateTNR } = require('../utils/tnrGenerator');
const { addToDatabase } = require('../utils/databaseUtils');

async function removeUserConcurrence(username) {
  try {
    await db.UserConcurrence.destroy({ where: { username } });
  } catch (error) {
    logger.error(`Error deleting user_concurrence for username ${username}:`, error);
    throw error;
  }
}

async function fetchAllPatients({ hospital, status, limit, offset }) {
  try {
    const { count, rows: patients } = await db.Patient.findAndCountAll({ 
      attributes: ['fullname', 'TNR'],
      where: {
        hospital,
        status: { [Op.like]: `${status}%` },
      },
      include: [
        {
          model: db.RegisterCriteria,
          as: 'register_criteria',
          required: true,
          attributes: [],
        },
        {
          model: db.SectionProgress,
          as: 'section_progress',
          required: true,
          attributes: [],
          where: { hospital },
        },
      ],
      raw: true,
      limit,
      offset
    });

    const referStatuses = await db.sequelize.query(
      `SELECT TNR, status FROM (SELECT * FROM refer WHERE hospital = ? ORDER BY refer_id DESC) AS referx GROUP BY TNR`,
      {
        replacements: [hospital],
        type: Sequelize.QueryTypes.SELECT,
      }
    );
    const referStatusMap = {};
    referStatuses.forEach(row => {
      referStatusMap[row.TNR] = row.status;
    });

    const result = patients.map(p => ({
      fullname: p.fullname,
      TNR: p.TNR,
      refer_status: referStatusMap[p.TNR] || null,
    }));
    return { count, rows: result };
  } catch (error) {
    logger.error(`Error fetching patients for hospital ${hospital} and status ${status}:`, error);
    throw error;
  }
}

/**
 * Filters patients based on dynamic criteria, including symptoms, date, completion, and status.
 * @param {string} hospital - Hospital identifier
 * @param {object} filters - Filtering options from request body
 * @returns {Promise<Array>} Filtered patient list with refer status
 */
async function filterPatientsByCriteria(hospital, filters, limit, offset) {
  try {
    const where = { hospital };
    const criteriaWhere = {};
    const progressWhere = { hospital };

    // Date filters
    if (filters.date_start) {
      where.created_date = { [Op.gte]: filters.date_start };
    }
    if (filters.date_stop) {
      where.created_date = { ...(where.created_date || {}), [Op.lte]: filters.date_stop };
    }

    // Symptoms filter
    if (filters.GA || filters.BW || filters.HIE || filters.major) {
      criteriaWhere[Op.or] = [];
      if (filters.GA) criteriaWhere[Op.or].push({ patient_symptoms: { [Op.like]: '%GA%' } });
      if (filters.BW) criteriaWhere[Op.or].push({ patient_symptoms: { [Op.like]: '%BW%' } });
      if (filters.HIE) criteriaWhere[Op.or].push({ patient_symptoms: { [Op.like]: '%HIE%' } });
      if (filters.major) criteriaWhere[Op.or].push({ patient_symptoms: { [Op.like]: '%Major%' } });
    }

    // Completion filter
    const completeSections = [
      'section1', 'section2', 'section3', 'section4', 'section5_1', 'section5_2', 'section5_3',
      'section5_4', 'section5_5', 'section5_6', 'section5_7', 'section5_8', 'section5_9',
      'section5_10', 'section5_11', 'section5_12'
    ];
    if (filters.complete === 'true' || filters.incomplete === 'true') {
      if (filters.complete === 'true' && filters.incomplete === 'true') {
        progressWhere[Op.or] = [
          { [Op.and]: completeSections.map(s => ({ [s]: { [Op.ne]: 'not done' } })) },
          { [Op.or]: completeSections.map(s => ({ [s]: { [Op.ne]: 'done' } })) }
        ];
      } else if (filters.complete === 'true') {
        progressWhere[Op.and] = completeSections.map(s => ({ [s]: { [Op.ne]: 'not done' } }));
      } else if (filters.incomplete === 'true') {
        progressWhere[Op.or] = completeSections.map(s => ({ [s]: { [Op.ne]: 'done' } }));
      }
    }

    // Status filter
    if (filters.status === 'active') {
      where.status = 'active';
    } else if (filters.status === 'inactive') {
      where.status = { [Op.like]: '%inactive%' };
    }

    // Sorting
    const order = [[filters.sort_by || 'created_date', (filters.sort_order || 'DESC').toUpperCase()]];

    // Query
    const { count, rows: patients } = await db.Patient.findAndCountAll({
      where,
      include: [
        {
          model: db.RegisterCriteria,
          as: 'register_criteria',
          required: false,
          where: Object.keys(criteriaWhere).length ? criteriaWhere : undefined,
        },
        {
          model: db.SectionProgress,
          as: 'section_progress',
          required: false,
          where: Object.keys(progressWhere).length ? progressWhere : undefined,
        },
      ],
      order,
      // raw: true,
      // nest: true,
      limit,
      offset
    });

    // Add refer status (latest per TNR)
    const tnrs = patients.map(p => p.TNR);
    let referStatusMap = {};
    if (tnrs.length) {
      const referStatuses = await db.sequelize.query(
        `SELECT TNR, status FROM (SELECT * FROM refer WHERE hospital = ? AND TNR IN (?) ORDER BY refer_id DESC) AS referx GROUP BY TNR`,
        {
          replacements: [hospital, tnrs],
          type: Sequelize.QueryTypes.SELECT,
        }
      );
      referStatuses.forEach(row => {
        referStatusMap[row.TNR] = row.status;
      });
    }

    // Attach refer_status
    const result = patients.map(p => ({
      ...p,
      refer_status: referStatusMap[p.TNR] || null
    }));
    return { count, rows: result };
  } catch (error) {
    logger.error('Error filtering patients:', error);
    throw error;
  }
}

/**
 * Fetches a patient by TNR and hospital.
 * @param {string} TNR
 * @param {string} hospital
 * @returns {Promise<Object|null>} Patient record or null
 */
async function getPatientByTnrHospital(TNR, hospital) {
  return await db.Patient.findOne({ where: { TNR, hospital } });
}

/**
 * Fetches all section_progress records by TNR and hospital.
 * @param {string} TNR
 * @param {string} hospital
 * @returns {Promise<Array>} Array of section_progress records
 */
async function getSectionProgressByTnrHospital(TNR, hospital) {
  return await db.SectionProgress.findAll({ where: { TNR, hospital } });
}

/**
 * Fetches all register_progress records by TNR.
 * @param {string} TNR
 * @returns {Promise<Array>} Array of register_progress records
 */
async function getRegisterProgressByTnr(TNR) {
  return await db.RegisterProgress.findAll({ where: { TNR } });
}

/**
 * Counts refer records by TNR and hospital with status 'waiting' or 'non-mem refer'.
 * @param {string} TNR
 * @param {string} hospital
 * @returns {Promise<number>} Count of matching refer records
 */
async function countReferByTnrAndHospital(TNR, hospital) {
  return await db.Refer.count({
    where: {
      TNR,
      hospital,
      status: { [db.Sequelize.Op.or]: ['waiting', 'non-mem refer'] }
    }
  });
}

/**
 * Updates refer status to 'cancel' for a given TNR and hospital.
 * @param {string} TNR
 * @param {string} hospital
 * @returns {Promise<number>} Number of affected rows
 */
async function cancelReferByTnrAndHospital(TNR, hospital) {
  const [affectedRows] = await db.Refer.update(
    { status: 'cancel' },
    { where: { TNR, hospital } }
  );
  return affectedRows;
}

/**
 * Updates refer status from 'reject' to 'reject2' for a given TNR and hospital.
 * @param {string} TNR
 * @param {string} hospital
 * @returns {Promise<number>} Number of affected rows
 */
async function updateReferStatusToReject2(TNR, hospital) {
  const [affectedRows] = await db.Refer.update(
    { status: 'reject2' },
    { where: { TNR, hospital, status: 'reject' } }
  );
  return affectedRows;
}

/**
 * Updates patient status to 'inactive' for a given TNR and hospital.
 * @param {string} TNR
 * @param {string} hospital
 * @returns {Promise<number>} Number of affected rows
 */
async function setPatientStatusInactive(TNR, hospital) {
  const [affectedRows] = await db.Patient.update(
    { status: 'inactive' },
    { where: { TNR, hospital } }
  );
  return affectedRows;
}

/**
 * Generates and inserts a TNR into patient_concurrence table with retry logic
 * Mimics the PHP logic from the original code
 * @param {string} registDate - Registration date in format YYYY-MM-DD
 * @param {string} hospital - Hospital identifier
 * @returns {Promise<string|number>} Generated TNR on success, 0 on failure
 */
async function generateAndInsertTNR(registDate, hospital) {
  const maxRetries = 3;

  for (let i = 0; i < maxRetries; i++) {
    try {
      // Generate a new TNR (now async)
      const tnr = await generateTNR(registDate, hospital);

      // Try to insert into patient_concurrence
      await db.PatientConcurrence.create({ TNR: tnr });

      logger.info(`Successfully generated and inserted TNR: ${tnr} for hospital: ${hospital}`);
      return tnr;
    } catch (error) {
      logger.warn(`Attempt ${i + 1} failed to insert TNR for hospital ${hospital}:`, error.message);

      // If this is the last attempt, log the final failure
      if (i === maxRetries - 1) {
        logger.error(`Failed to generate TNR after ${maxRetries} attempts for hospital ${hospital}:`, error);
        return 0;
      }

      // Wait 1 second before retry (mimicking PHP sleep(1))
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  return 0;
}

/**
 * Creates a new patient record with section progress
 * Accepts patient data as JSON object
 * @param {string} TNR - The TNR for the patient
 * @param {Object} patientData - Patient data as JSON object
 * @param {string} hospital - Hospital identifier
 * @returns {Promise<string>} Success message
 */
async function createPatientWithProgress(TNR, patientData, hospital) {
  try {
    // Add created_date like PHP: $created_date = date("Y-m-d H:i:s");
    const createdDate = new Date().toISOString().slice(0, 19).replace('T', ' ');

    // Ensure patientData is an object and add created_date
    const finalPatientData = {
      ...patientData,
      TNR: TNR, // Ensure TNR is included
      created_date: createdDate,
      hospital: hospital // Set hospital directly
    };

    // Insert into patient table using generic function
    await addToDatabase('patient', finalPatientData);

    // Insert into section_progress table (like PHP INSERT query)
    await db.SectionProgress.create({
      TNR: TNR,
      hospital: hospital,
      time: createdDate
    });

    logger.info(`Successfully created patient with TNR: ${TNR} for hospital: ${hospital}`);
    return "ok";

  } catch (error) {
    logger.error(`Error creating patient with TNR ${TNR}:`, error);
    throw error;
  }
}

module.exports = {
  removeUserConcurrence,
  fetchAllPatients,
  filterPatientsByCriteria,
  getPatientByTnrHospital,
  getSectionProgressByTnrHospital,
  getRegisterProgressByTnr,
  countReferByTnrAndHospital,
  cancelReferByTnrAndHospital,
  updateReferStatusToReject2,
  setPatientStatusInactive,
  generateAndInsertTNR,
  createPatientWithProgress
};