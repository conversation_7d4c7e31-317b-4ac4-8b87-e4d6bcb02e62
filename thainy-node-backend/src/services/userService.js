const db = require('../models');
const logger = require('../utils/logger');

async function getUserData({ username, email, hospital }) {
  try {
    return await db.User.findOne({ where: { username, email, hospital } });
  } catch (error) {
    logger.error(`Error fetching user data for username ${username}, email ${email}, hospital ${hospital}:`, error);
    throw error;
  }
}

/**
 * Handles user concurrence for a TNR, matching the PHP logic.
 * @param {string} username
 * @param {string} TNR
 * @returns {Promise<{status: number, criteria?: string, TNR?: string}>}
 */
async function handleUserConcurrence(username, TNR) {
  // const db = require('../models');
  const now = new Date();
  const nowStr = now.toISOString().slice(0, 19).replace('T', ' ');
  // Check if a record exists for this TNR and a future time
  const existing = await db.UserConcurrence.findOne({
    where: {
      TNR,
      time: { [db.Sequelize.Op.gt]: nowStr }
    }
  });
  if (!existing) {
    // Delete any old records for this TNR
    await db.UserConcurrence.destroy({ where: { TNR } });
    // Insert new record for this user, TNR, and time +15 min
    const newTime = new Date(now.getTime() + 15 * 60000);
    const newTimeStr = newTime.toISOString().slice(0, 19).replace('T', ' ');
    await db.UserConcurrence.create({ username, TNR, time: newTimeStr });
    // Fetch criteria from register_criteria
    const criteriaRow = await db.RegisterCriteria.findOne({ where: { TNR } });
    const criteria = criteriaRow ? criteriaRow.patient_symptoms : null;
    return { status: 1, criteria, TNR };
  } else {
    return { status: 0 };
  }
}

module.exports = { getUserData, handleUserConcurrence };
