const { createRegisterCriteria } = require('../services/registerCriteriaService');
const logger = require('../utils/logger');

/**
 * POST /register-criteria/create
 * Body: { isdata: "JSON_STRING" } - Mimics PHP $_POST["isdata"]
 * Creates a new register criteria record
 */
async function createRegisterCriteriaHandler(req, res) {
  try {
    const { isdata } = req.body;

    // Validate required field
    if (!isdata) {
      return res.status(400).json({
        status: 0,
        message: 'isdata field is required.'
      });
    }

    // Parse JSON data (like PHP json_decode)
    let criteriaData;
    try {
      criteriaData = JSON.parse(isdata);
    } catch (parseError) {
      return res.status(400).json({
        status: 0,
        message: 'Invalid JSON format in isdata field.'
      });
    }

    // Validate required fields for register_criteria
    if (!criteriaData.TNR || !criteriaData.ward || !criteriaData.pna || !criteriaData.patient_symptoms) {
      return res.status(400).json({
        status: 0,
        message: 'TNR, ward, pna, and patient_symptoms are required fields.'
      });
    }

    // Create the register criteria record
    await createRegisterCriteria(criteriaData);

    // Return the data like PHP (echo $mydata)
    res.json(criteriaData);

  } catch (error) {
    logger.error('Error creating register criteria:', error);

    // Handle duplicate key error
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({
        status: 0,
        message: 'Register criteria with this TNR already exists.'
      });
    }

    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

module.exports = {
  createRegisterCriteriaHandler
};
