const { removeUserConcurrence, fetchAllPatients, filterPatientsByCriteria, getPatientByTnrHospital, getSectionProgressByTnrHospital, getRegisterProgressByTnr, countReferByTnrAndHospital, cancelReferByTnrAndHospital, updateReferStatusToReject2, setPatientStatusInactive, generateAndInsertTNR, createPatientWithProgress } = require('../services/patientService');
const logger = require('../utils/logger');
const { getPagination, getPagingData } = require('../utils/pagination');

async function getAllPatients(req, res) {
  try {
    const { hospital, username } = req.user;
    const { status } = req.query;
    if (!status) {
      return res.status(400).json({ message: 'Status is required as a query parameter.' });
    }
    await removeUserConcurrence(username);
    const { limit, offset } = getPagination(req.query, 10, 100);
    const data = await fetchAllPatients({ hospital, status, limit, offset });
    const page = req.query.page ? parseInt(req.query.page, 10) : Math.floor(offset / limit) + 1;
    const response = getPagingData(data, page, limit);
    res.json(response);
  } catch (error) {
    logger.error('Error fetching patients:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

async function filterPatients(req, res) {
  try {
    const { hospital } = req.user;
    const filters = req.body;
    const { limit, offset } = getPagination(req.query, 10, 100);
    const data = await filterPatientsByCriteria(hospital, filters, limit, offset);
    const page = req.query.page ? parseInt(req.query.page, 10) : Math.floor(offset / limit) + 1;
    const response = getPagingData(data, page, limit);
    res.json(response);
  } catch (error) {
    logger.error('Error filtering patients:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

/**
 * POST /patient/by-tnr-hospital
 * Body: { TNR, hospital }
 * Returns the patient record for the given TNR and hospital.
 */
async function getPatientByTnrAndHospital(req, res) {
  try {
    const TNR = req.body.TNR || req.user?.TNR;
    let hospital = req.body.hospital || req.user?.hospital;
    if (!TNR || !hospital) {
      return res.status(400).json({ message: 'TNR and hospital are required.' });
    }
    const patient = await getPatientByTnrHospital(TNR, hospital);
    if (!patient) {
      return res.status(404).json({ message: 'No patient found for the given TNR and hospital.' });
    }
    res.json(patient);
  } catch (error) {
    console.error('Error fetching patient by TNR and hospital:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

/**
 * POST /patient/section-progress
 * Body: { TNR, hospital }
 * Returns all section_progress records for the given TNR and hospital.
 */
async function getSectionProgressByTnrAndHospital(req, res) {
  try {
    const TNR = req.body.TNR || req.user?.TNR;
    let hospital = req.body.hospital || req.user?.hospital;
    if (!TNR || !hospital) {
      return res.status(400).json({ message: 'TNR and hospital are required.' });
    }
    const progress = await getSectionProgressByTnrHospital(TNR, hospital);
    if (!progress || progress.length === 0) {
      return res.status(404).json({ message: 'No section progress found for the given TNR and hospital.' });
    }
    res.json(progress);
  } catch (error) {
    console.error('Error fetching section_progress by TNR and hospital:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

/**
 * POST /patients/register-progress
 * Body: { TNR }
 * Returns all register_progress records for the given TNR.
 */
async function getRegisterProgressByTnrHandler(req, res) {
  try {
    const TNR = req.body.TNR || req.user?.TNR;
    if (!TNR) {
      return res.status(400).json({ message: 'TNR is required.' });
    }
    const progress = await getRegisterProgressByTnr(TNR);
    if (!progress || progress.length === 0) {
      return res.status(404).json({ message: 'No register progress found for the given TNR.' });
    }
    res.json(progress);
  } catch (error) {
    console.error('Error fetching register_progress by TNR:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

/**
 * POST /patients/refer-count
 * Body: { TNR, hospital }
 * Returns the count of refer records for the given TNR and hospital with status 'waiting' or 'non-mem refer'.
 */
async function countReferByTnrAndHospitalHandler(req, res) {
  try {
    const TNR = req.body.TNR || req.user?.TNR;
    let hospital = req.body.hospital || req.user?.hospital;
    if (!TNR || !hospital) {
      return res.status(400).json({ message: 'TNR and hospital are required.' });
    }
    const count = await countReferByTnrAndHospital(TNR, hospital);
    res.json({ count });
  } catch (error) {
    console.error('Error counting refer records by TNR and hospital:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

/**
 * POST /patients/cancel-refer
 * Body: { TNR, hospital }
 * Updates refer status to 'cancel' for the given TNR and hospital.
 */
async function cancelReferByTnrAndHospitalHandler(req, res) {
  try {
    const TNR = req.body.TNR || req.user?.TNR;
    let hospital = req.body.hospital || req.user?.hospital;
    if (!TNR || !hospital) {
      return res.status(400).json({ message: 'TNR and hospital are required.' });
    }
    const affectedRows = await cancelReferByTnrAndHospital(TNR, hospital);
    if (affectedRows === 0) {
      return res.status(404).json({ message: 'No refer records found to update for the given TNR and hospital.' });
    }
    res.json({ message: 'Refer status updated to cancel.', affectedRows });
  } catch (error) {
    console.error('Error updating refer status to cancel:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

/**
 * POST /patients/reject2-refer
 * Body: { TNR, hospital }
 * Updates refer status from 'reject' to 'reject2' for the given TNR and hospital.
 */
async function updateReferStatusToReject2Handler(req, res) {
  try {
    const TNR = req.body.TNR || req.user?.TNR;
    let hospital = req.body.hospital || req.user?.hospital;
    if (!TNR || !hospital) {
      return res.status(400).json({ message: 'TNR and hospital are required.' });
    }
    const affectedRows = await updateReferStatusToReject2(TNR, hospital);
    if (affectedRows === 0) {
      return res.status(404).json({ message: 'No refer records with status reject found to update for the given TNR and hospital.' });
    }
    res.json({ message: 'Refer status updated to reject2.', affectedRows });
  } catch (error) {
    console.error('Error updating refer status to reject2:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

/**
 * POST /patients/set-inactive
 * Body: { TNR, hospital }
 * Sets patient status to 'inactive' for the given TNR and hospital.
 */
async function setPatientStatusInactiveHandler(req, res) {
  try {
    const TNR = req.body.TNR || req.user?.TNR;
    let hospital = req.body.hospital || req.user?.hospital;
    if (!TNR || !hospital) {
      return res.status(400).json({ message: 'TNR and hospital are required.' });
    }
    const affectedRows = await setPatientStatusInactive(TNR, hospital);
    if (affectedRows === 0) {
      return res.status(404).json({ message: 'No patient found to update for the given TNR and hospital.' });
    }
    res.json({ message: 'Patient status updated to inactive.', affectedRows });
  } catch (error) {
    console.error('Error updating patient status to inactive:', error);
    res.status(500).json({ message: 'Internal server error' });
  }
}

/**
 * POST /patients/generate-tnr
 * Body: { regist_date }
 * Generates a TNR and inserts it into patient_concurrence table
 * Uses hospital from authenticated user session
 */
async function generateTNRHandler(req, res) {
  try {
    const { regist_date } = req.body;
    const hospital = req.user?.hospital;

    // Validate required fields
    if (!regist_date) {
      return res.status(400).json({
        status: 0,
        message: 'Registration date (regist_date) is required.'
      });
    }

    if (!hospital) {
      return res.status(400).json({
        status: 0,
        message: 'Hospital information is required. Please ensure you are authenticated.'
      });
    }

    // Validate date format
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(regist_date)) {
      return res.status(400).json({
        status: 0,
        message: 'Invalid date format. Please use YYYY-MM-DD format.'
      });
    }

    // Generate and insert TNR
    const result = await generateAndInsertTNR(regist_date, hospital);

    if (result === 0) {
      return res.json(0);
    }

    // Return success response with generated TNR (matching PHP response format)
    res.json(result);

  } catch (error) {
    logger.error('Error generating TNR:', error);
    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

/**
 * POST /patients/create
 * Body: JSON object with patient data including TNR
 * Creates a new patient with section progress
 */
async function createPatientHandler(req, res) {
  try {
    const { TNR, ...patientData } = req.body;
    const hospital = req.user?.hospital;

    // Validate required fields
    if (!TNR) {
      return res.status(400).json({
        status: 0,
        message: 'TNR is required.'
      });
    }

    if (!hospital) {
      return res.status(400).json({
        status: 0,
        message: 'Hospital information is required. Please ensure you are authenticated.'
      });
    }

    // Validate that we have patient data
    if (!patientData || Object.keys(patientData).length === 0) {
      return res.status(400).json({
        status: 0,
        message: 'Patient data is required.'
      });
    }

    // Validate critical fields (optional but recommended)
    if (!patientData.fullname) {
      return res.status(400).json({
        status: 0,
        message: 'Patient fullname is required.'
      });
    }

    // Create patient with progress
    const result = await createPatientWithProgress(TNR, patientData, hospital);

    // Return "ok" like PHP
    res.json(result);

  } catch (error) {
    logger.error('Error creating patient:', error);

    // Handle duplicate key error
    if (error.name === 'SequelizeUniqueConstraintError') {
      return res.status(409).json({
        status: 0,
        message: 'Patient with this TNR already exists.'
      });
    }

    res.status(500).json({
      status: 0,
      message: 'Internal server error'
    });
  }
}

module.exports = {
  getAllPatients,
  filterPatients,
  getPatientByTnrAndHospital,
  getSectionProgressByTnrAndHospital,
  getRegisterProgressByTnrHandler,
  countReferByTnrAndHospitalHandler,
  cancelReferByTnrAndHospitalHandler,
  updateReferStatusToReject2Handler,
  setPatientStatusInactiveHandler,
  generateTNRHandler,
  createPatientHandler
};